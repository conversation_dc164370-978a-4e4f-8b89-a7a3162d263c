import { z } from 'zod';
export interface AgentConfig {
    provider: AIProvider;
    model: string;
    apiKey?: string;
    baseUrl?: string;
    maxTokens?: number;
    temperature?: number;
    approvalMode: ApprovalMode;
    sessionId?: string;
    workingDirectory: string;
    maxContextTokens?: number;
    messageHistoryLimit?: number;
}
export type AIProvider = 'openai' | 'deepseek' | 'ollama' | 'azure';
export type ApprovalMode = 'suggest' | 'auto-edit' | 'full-auto';
export interface Session {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    workingDirectory: string;
    context: SessionContext;
    messages: ConversationMessage[];
    metadata: SessionMetadata;
}
export interface SessionContext {
    projectStructure: ProjectStructure;
    environmentInfo: EnvironmentInfo;
    recentFiles: string[];
    activeProcesses: ProcessInfo[];
    variables: Record<string, unknown>;
}
export interface SessionMetadata {
    totalCommands: number;
    successfulCommands: number;
    failedCommands: number;
    lastActivity: Date;
    tags: string[];
}
export interface ConversationMessage {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    timestamp: Date;
    toolCalls?: ToolCall[];
    toolResults?: ToolResult[];
    toolCallId?: string;
    metadata?: MessageMetadata;
}
export interface MessageMetadata {
    tokens?: number;
    model?: string;
    provider?: AIProvider;
    executionTime?: number;
    approved?: boolean;
}
export interface ToolCall {
    id: string;
    name: string;
    arguments: Record<string, unknown>;
    approved?: boolean;
    riskLevel: RiskLevel;
}
export interface ToolResult {
    id: string;
    toolCallId: string;
    success: boolean;
    output: string;
    error?: string;
    executionTime: number;
    metadata?: Record<string, unknown>;
}
export type RiskLevel = 'safe' | 'low' | 'medium' | 'high' | 'critical';
export interface FileOperation {
    type: 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'search' | 'grep';
    path: string;
    content?: string;
    destination?: string;
    pattern?: string;
    options?: FileOperationOptions;
}
export interface FileOperationOptions {
    recursive?: boolean;
    force?: boolean;
    backup?: boolean;
    encoding?: string;
    permissions?: string;
}
export interface ShellCommand {
    command: string;
    args: string[];
    cwd?: string;
    env?: Record<string, string>;
    timeout?: number;
    shell?: boolean;
}
export interface CommandResult {
    success: boolean;
    exitCode: number;
    stdout: string;
    stderr: string;
    executionTime: number;
    command: string;
}
export interface ProjectStructure {
    root: string;
    type: ProjectType;
    packageManager?: PackageManager;
    framework?: string;
    language?: string;
    files: FileNode[];
    dependencies: Dependency[];
    scripts: Record<string, string>;
}
export type ProjectType = 'node' | 'python' | 'rust' | 'go' | 'java' | 'unknown';
export type PackageManager = 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo' | 'go' | 'maven' | 'gradle';
export interface FileNode {
    path: string;
    type: 'file' | 'directory';
    size?: number;
    modified?: Date;
    children?: FileNode[];
}
export interface Dependency {
    name: string;
    version: string;
    type: 'production' | 'development' | 'peer' | 'optional';
}
export interface EnvironmentInfo {
    platform: NodeJS.Platform;
    arch: string;
    nodeVersion: string;
    npmVersion?: string;
    gitVersion?: string;
    shell: string;
    terminal: string;
    workingDirectory: string;
    homeDirectory: string;
    pathVariable: string[];
}
export interface ProcessInfo {
    id?: string;
    pid: number;
    name: string;
    command: string;
    args?: string[];
    status: 'running' | 'stopped' | 'zombie' | 'completed' | 'failed' | 'killed';
    startTime: Date;
    endTime?: Date;
    exitCode?: number;
    cpuUsage?: number;
    memoryUsage?: number;
}
export interface SecurityPolicy {
    allowedCommands: string[];
    blockedCommands: string[];
    allowedPaths: string[];
    blockedPaths: string[];
    requireApproval: string[];
    maxExecutionTime: number;
    sandboxMode: boolean;
}
export interface RiskAssessment {
    level: RiskLevel;
    reasons: string[];
    suggestions: string[];
    autoApprove: boolean;
}
export declare const ConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["openai", "deepseek", "ollama", "azure"]>;
    model: z.ZodString;
    apiKey: z.ZodOptional<z.ZodString>;
    baseUrl: z.ZodOptional<z.ZodString>;
    maxTokens: z.ZodOptional<z.ZodNumber>;
    temperature: z.ZodOptional<z.ZodNumber>;
    approvalMode: z.ZodEnum<["suggest", "auto-edit", "full-auto"]>;
    workingDirectory: z.ZodString;
    security: z.ZodOptional<z.ZodObject<{
        allowedCommands: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        blockedCommands: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        maxExecutionTime: z.ZodOptional<z.ZodNumber>;
        sandboxMode: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
    }, {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    provider: "openai" | "deepseek" | "ollama" | "azure";
    model: string;
    approvalMode: "suggest" | "auto-edit" | "full-auto";
    workingDirectory: string;
    security?: {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
    } | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    maxTokens?: number | undefined;
    temperature?: number | undefined;
}, {
    provider: "openai" | "deepseek" | "ollama" | "azure";
    model: string;
    approvalMode: "suggest" | "auto-edit" | "full-auto";
    workingDirectory: string;
    security?: {
        allowedCommands?: string[] | undefined;
        blockedCommands?: string[] | undefined;
        maxExecutionTime?: number | undefined;
        sandboxMode?: boolean | undefined;
    } | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    maxTokens?: number | undefined;
    temperature?: number | undefined;
}>;
export type Config = z.infer<typeof ConfigSchema>;
export declare class AgentError extends Error {
    code: string;
    details?: Record<string, unknown> | undefined;
    constructor(message: string, code: string, details?: Record<string, unknown> | undefined);
}
export declare class ToolError extends AgentError {
    toolName: string;
    constructor(message: string, toolName: string, details?: Record<string, unknown>);
}
export declare class SecurityError extends AgentError {
    riskLevel: RiskLevel;
    constructor(message: string, riskLevel: RiskLevel, details?: Record<string, unknown>);
}
export interface AgentEvent {
    type: string;
    timestamp: Date;
    data: Record<string, unknown>;
}
export type EventHandler<T = Record<string, unknown>> = (event: AgentEvent & {
    data: T;
}) => void | Promise<void>;
export interface StreamChunk {
    type: 'text' | 'tool_call' | 'tool_result' | 'error' | 'done';
    content: string;
    metadata?: Record<string, unknown>;
}
export interface StreamHandler {
    onChunk: (chunk: StreamChunk) => void;
    onError: (error: Error) => void;
    onComplete: () => void;
}
export interface FileOperation {
    type: 'read' | 'write' | 'create' | 'delete' | 'move' | 'copy' | 'search' | 'grep';
    path: string;
    content?: string;
    destination?: string;
    pattern?: string;
    options?: FileOperationOptions;
}
export interface FileOperationOptions {
    encoding?: string;
    backup?: boolean;
    recursive?: boolean;
    force?: boolean;
    ignoreCase?: boolean;
    lineNumbers?: boolean;
    maxResults?: number;
    lines?: number;
    offset?: number;
}
export interface ToolChain {
    id: string;
    tools: ToolCall[];
    parallel?: boolean;
    continueOnError?: boolean;
}
export interface ChainResult {
    id: string;
    chainId: string;
    results: ToolResult[];
    success: boolean;
    executionTime: number;
}
//# sourceMappingURL=index.d.ts.map