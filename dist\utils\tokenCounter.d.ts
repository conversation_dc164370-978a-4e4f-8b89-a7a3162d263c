import type { ConversationMessage } from '../types';
/**
 * Simple token counting utility for managing context windows
 * Uses rough estimation: ~4 characters per token for most models
 */
export declare class TokenCounter {
    private static readonly CHARS_PER_TOKEN;
    private static readonly SAFETY_MARGIN;
    /**
     * Estimate token count for a string
     */
    static countTokens(text: string): number;
    /**
     * Estimate token count for a message
     */
    static countMessageTokens(message: ConversationMessage): number;
    /**
     * Estimate token count for multiple messages
     */
    static countMessagesTokens(messages: ConversationMessage[]): number;
    /**
     * Truncate messages to fit within token limit with sliding window
     */
    static truncateMessages(messages: ConversationMessage[], maxTokens: number, systemMessageTokens?: number, toolsTokens?: number): ConversationMessage[];
    /**
     * Truncate text to fit within token limit
     */
    static truncateText(text: string, maxTokens: number): string;
    /**
     * Get model-specific token limits
     */
    static getModelTokenLimit(model: string): number;
    /**
     * Calculate optimal message limit based on model and context
     */
    static calculateOptimalMessageLimit(model: string, systemMessageTokens: number, toolsTokens: number, maxCompletionTokens?: number): number;
}
//# sourceMappingURL=tokenCounter.d.ts.map