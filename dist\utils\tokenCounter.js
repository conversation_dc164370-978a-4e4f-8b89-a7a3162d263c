"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenCounter = void 0;
/**
 * Simple token counting utility for managing context windows
 * Uses rough estimation: ~4 characters per token for most models
 */
class TokenCounter {
    static CHARS_PER_TOKEN = 4;
    static SAFETY_MARGIN = 0.1; // 10% safety margin
    /**
     * Estimate token count for a string
     */
    static countTokens(text) {
        if (!text)
            return 0;
        return Math.ceil(text.length / this.CHARS_PER_TOKEN);
    }
    /**
     * Estimate token count for a message
     */
    static countMessageTokens(message) {
        let tokens = 0;
        // Count content tokens
        tokens += this.countTokens(message.content || '');
        // Count tool calls tokens
        if (message.toolCalls) {
            for (const toolCall of message.toolCalls) {
                tokens += this.countTokens(toolCall.name);
                tokens += this.countTokens(JSON.stringify(toolCall.arguments));
            }
        }
        // Add overhead for message structure (role, metadata, etc.)
        tokens += 10;
        return tokens;
    }
    /**
     * Estimate token count for multiple messages
     */
    static countMessagesTokens(messages) {
        return messages.reduce((total, message) => total + this.countMessageTokens(message), 0);
    }
    /**
     * Truncate messages to fit within token limit with sliding window
     */
    static truncateMessages(messages, maxTokens, systemMessageTokens = 0, toolsTokens = 0) {
        const availableTokens = Math.floor(maxTokens * (1 - this.SAFETY_MARGIN)) - systemMessageTokens - toolsTokens;
        if (availableTokens <= 0) {
            return [];
        }
        // Find the last user message index (compatible with older TypeScript)
        let lastUserMessageIndex = -1;
        for (let i = messages.length - 1; i >= 0; i--) {
            if (messages[i].role === 'user') {
                lastUserMessageIndex = i;
                break;
            }
        }
        const result = [];
        let currentTokens = 0;
        // Start from the end and work backwards (sliding window)
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            const messageTokens = this.countMessageTokens(message);
            // Always include the last user message
            if (i === lastUserMessageIndex) {
                result.unshift(message);
                currentTokens += messageTokens;
                continue;
            }
            // Check if we can fit this message
            if (currentTokens + messageTokens <= availableTokens) {
                result.unshift(message);
                currentTokens += messageTokens;
            }
            else {
                // Can't fit more messages, stop here
                break;
            }
        }
        return result;
    }
    /**
     * Truncate text to fit within token limit
     */
    static truncateText(text, maxTokens) {
        const estimatedTokens = this.countTokens(text);
        if (estimatedTokens <= maxTokens) {
            return text;
        }
        const maxChars = Math.floor(maxTokens * this.CHARS_PER_TOKEN * (1 - this.SAFETY_MARGIN));
        return text.substring(0, maxChars) + '...';
    }
    /**
     * Get model-specific token limits
     */
    static getModelTokenLimit(model) {
        const limits = {
            'gpt-4': 8192,
            'gpt-4-32k': 32768,
            'gpt-4-turbo': 128000,
            'gpt-4o': 128000,
            'gpt-3.5-turbo': 4096,
            'gpt-3.5-turbo-16k': 16384,
            'deepseek-chat': 65536,
            'deepseek-coder': 65536,
            'claude-3-sonnet': 200000,
            'claude-3-opus': 200000,
            'llama2': 4096,
            'llama2-70b': 4096,
            'codellama': 16384,
        };
        // Default to a conservative limit if model not found
        return limits[model] || 4096;
    }
    /**
     * Calculate optimal message limit based on model and context
     */
    static calculateOptimalMessageLimit(model, systemMessageTokens, toolsTokens, maxCompletionTokens = 4096) {
        const modelLimit = this.getModelTokenLimit(model);
        const availableForMessages = modelLimit - systemMessageTokens - toolsTokens - maxCompletionTokens;
        // Estimate average tokens per message (rough estimate)
        const avgTokensPerMessage = 150;
        return Math.max(1, Math.floor(availableForMessages / avgTokensPerMessage));
    }
}
exports.TokenCounter = TokenCounter;
//# sourceMappingURL=tokenCounter.js.map