import { EventEmitter } from 'events';
import { watch, FSWatcher } from 'chokidar';
import { glob } from 'glob';
import fs from 'fs/promises';
import path from 'path';
import os from 'os';
import { spawn } from 'cross-spawn';
import { logger } from '@/utils/logger';
import type {
  SessionContext,
  ProjectStructure,
  EnvironmentInfo,
  FileNode,
  Dependency,
  ProjectType,
  PackageManager,
} from '@/types';

export class ContextManager extends EventEmitter {
  private watcher: FSWatcher | null = null;
  private context: SessionContext;
  // private lastUpdate: Date = new Date();

  constructor(private workingDirectory: string) {
    super();
    this.context = {
      projectStructure: {
        root: workingDirectory,
        type: 'unknown',
        files: [],
        dependencies: [],
        scripts: {},
      },
      environmentInfo: {} as EnvironmentInfo,
      recentFiles: [],
      activeProcesses: [],
      variables: {},
    };
  }

  async initialize(): Promise<void> {
    try {
      await this.discoverProjectStructure();
      await this.gatherEnvironmentInfo();
      await this.indexProjectFiles();
      await this.discoverDependencies();
      await this.startFileWatcher();

      // this.lastUpdate = new Date();
      logger.info('Context manager initialized', {
        workingDirectory: this.workingDirectory,
        projectType: this.context.projectStructure.type,
        fileCount: this.context.projectStructure.files.length,
        dependencyCount: this.context.projectStructure.dependencies.length
      });
    } catch (error) {
      logger.error('Failed to initialize context manager', error);
      throw error;
    }
  }

  async getContext(): Promise<SessionContext> {
    // Return a lightweight version of context to save tokens
    return {
      projectStructure: {
        root: this.context.projectStructure.root,
        type: this.context.projectStructure.type,
        language: this.context.projectStructure.language,
        framework: this.context.projectStructure.framework,
        packageManager: this.context.projectStructure.packageManager,
        // Limit files to avoid token overflow - only include essential files
        files: this.context.projectStructure.files.slice(0, 20),
        // Limit dependencies to avoid token overflow
        dependencies: this.context.projectStructure.dependencies.slice(0, 10),
        scripts: this.context.projectStructure.scripts,
      },
      environmentInfo: {
        platform: this.context.environmentInfo.platform,
        nodeVersion: this.context.environmentInfo.nodeVersion,
        arch: this.context.environmentInfo.arch,
        shell: this.context.environmentInfo.shell,
        terminal: this.context.environmentInfo.terminal,
        workingDirectory: this.context.environmentInfo.workingDirectory,
        homeDirectory: this.context.environmentInfo.homeDirectory,
        pathVariable: this.context.environmentInfo.pathVariable,
      },
      // Limit recent files to save tokens
      recentFiles: this.context.recentFiles.slice(0, 5),
      // Limit active processes to save tokens
      activeProcesses: this.context.activeProcesses.slice(0, 3),
      variables: this.context.variables,
    };
  }

  async updateContext(): Promise<void> {
    try {
      await this.discoverProjectStructure();
      await this.updateActiveProcesses();
      // this.lastUpdate = new Date();
      
      this.emit('context-updated', this.context);
      logger.debug('Context updated');
    } catch (error) {
      logger.error('Failed to update context', error);
    }
  }

  async setWorkingDirectory(directory: string): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
    }
    
    this.workingDirectory = directory;
    this.context.projectStructure.root = directory;
    
    await this.initialize();
  }

  private async discoverProjectStructure(): Promise<void> {
    try {
      const structure: ProjectStructure = {
        root: this.workingDirectory,
        type: 'unknown',
        files: [],
        dependencies: [],
        scripts: {},
      };

      // Detect project type and package manager
      const detectionResult = await this.detectProjectType();
      structure.type = detectionResult.type;
      structure.packageManager = detectionResult.packageManager;
      structure.framework = detectionResult.framework;
      structure.language = detectionResult.language;

      // Scan files
      structure.files = await this.scanFiles();

      // Parse dependencies and scripts
      if (structure.packageManager) {
        const packageInfo = await this.parsePackageInfo(structure.packageManager);
        structure.dependencies = packageInfo.dependencies;
        structure.scripts = packageInfo.scripts;
      }

      this.context.projectStructure = structure;
    } catch (error) {
      logger.error('Failed to discover project structure', error);
    }
  }

  private async detectProjectType(): Promise<{
    type: ProjectType;
    packageManager?: PackageManager;
    framework?: string;
    language?: string;
  }> {
    const files = await fs.readdir(this.workingDirectory).catch(() => []);
    const fileSet = new Set(files);

    // Node.js projects
    if (fileSet.has('package.json')) {
      const packageJson = await this.readJsonFile('package.json');
      const packageManager = await this.detectNodePackageManager();
      
      let framework: string | undefined;
      if (packageJson?.dependencies || packageJson?.devDependencies) {
        const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        if (deps.react) framework = 'React';
        else if (deps.vue) framework = 'Vue';
        else if (deps.angular) framework = 'Angular';
        else if (deps.next) framework = 'Next.js';
        else if (deps.nuxt) framework = 'Nuxt.js';
        else if (deps.express) framework = 'Express';
        else if (deps.fastify) framework = 'Fastify';
      }
      
      return {
        type: 'node',
        packageManager,
        framework,
        language: files.some(f => f.endsWith('.ts')) ? 'TypeScript' : 'JavaScript',
      };
    }

    // Python projects
    if (fileSet.has('requirements.txt') || fileSet.has('pyproject.toml') || fileSet.has('setup.py')) {
      return {
        type: 'python',
        packageManager: 'pip',
        language: 'Python',
      };
    }

    // Rust projects
    if (fileSet.has('Cargo.toml')) {
      return {
        type: 'rust',
        packageManager: 'cargo',
        language: 'Rust',
      };
    }

    // Go projects
    if (fileSet.has('go.mod')) {
      return {
        type: 'go',
        packageManager: 'go',
        language: 'Go',
      };
    }

    // Java projects
    if (fileSet.has('pom.xml')) {
      return {
        type: 'java',
        packageManager: 'maven',
        language: 'Java',
      };
    }

    if (fileSet.has('build.gradle') || fileSet.has('build.gradle.kts')) {
      return {
        type: 'java',
        packageManager: 'gradle',
        language: 'Java',
      };
    }

    return { type: 'unknown' };
  }

  private async detectNodePackageManager(): Promise<PackageManager> {
    const files = await fs.readdir(this.workingDirectory).catch(() => []);
    const fileSet = new Set(files);

    if (fileSet.has('pnpm-lock.yaml')) return 'pnpm';
    if (fileSet.has('yarn.lock')) return 'yarn';
    if (fileSet.has('package-lock.json')) return 'npm';

    return 'npm'; // Default
  }

  private async scanFiles(): Promise<FileNode[]> {
    try {
      const patterns = [
        '**/*',
        '!node_modules/**',
        '!.git/**',
        '!dist/**',
        '!build/**',
        '!target/**',
        '!__pycache__/**',
        '!*.pyc',
        '!.env*',
      ];

      const files = await glob(patterns, {
        cwd: this.workingDirectory,
        dot: false,
        maxDepth: 3, // Limit depth for performance
      });

      const fileNodes: FileNode[] = [];

      for (const file of files.slice(0, 100)) { // Limit for performance
        try {
          const fullPath = path.join(this.workingDirectory, file);
          const stats = await fs.stat(fullPath);
          
          fileNodes.push({
            path: file,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.isFile() ? stats.size : undefined,
            modified: stats.mtime,
          });
        } catch (error) {
          // Skip files that can't be accessed
        }
      }

      return fileNodes;
    } catch (error) {
      logger.error('Failed to scan files', error);
      return [];
    }
  }

  private async parsePackageInfo(packageManager: PackageManager): Promise<{
    dependencies: Dependency[];
    scripts: Record<string, string>;
  }> {
    const result = { dependencies: [] as Dependency[], scripts: {} as Record<string, string> };

    try {
      switch (packageManager) {
        case 'npm':
        case 'yarn':
        case 'pnpm':
          const packageJson = await this.readJsonFile('package.json');
          if (packageJson) {
            result.scripts = packageJson.scripts || {};
            
            // Parse dependencies
            const deps = packageJson.dependencies || {};
            const devDeps = packageJson.devDependencies || {};
            const peerDeps = packageJson.peerDependencies || {};
            
            for (const [name, version] of Object.entries(deps)) {
              result.dependencies.push({ name, version: version as string, type: 'production' });
            }
            
            for (const [name, version] of Object.entries(devDeps)) {
              result.dependencies.push({ name, version: version as string, type: 'development' });
            }
            
            for (const [name, version] of Object.entries(peerDeps)) {
              result.dependencies.push({ name, version: version as string, type: 'peer' });
            }
          }
          break;

        case 'pip':
          // Parse requirements.txt
          try {
            const requirements = await fs.readFile(
              path.join(this.workingDirectory, 'requirements.txt'),
              'utf-8'
            );
            
            for (const line of requirements.split('\n')) {
              const trimmed = line.trim();
              if (trimmed && !trimmed.startsWith('#')) {
                const match = trimmed.match(/^([^>=<]+)/);
                if (match) {
                  result.dependencies.push({
                    name: match[1],
                    version: trimmed.replace(match[1], ''),
                    type: 'production',
                  });
                }
              }
            }
          } catch (error) {
            // requirements.txt might not exist
          }
          break;

        case 'cargo':
          const cargoToml = await this.readTomlFile('Cargo.toml');
          if (cargoToml?.dependencies) {
            for (const [name, version] of Object.entries(cargoToml.dependencies)) {
              result.dependencies.push({
                name,
                version: typeof version === 'string' ? version : JSON.stringify(version),
                type: 'production',
              });
            }
          }
          break;
      }
    } catch (error) {
      logger.error('Failed to parse package info', error);
    }

    return result;
  }



  private async indexProjectFiles(): Promise<void> {
    try {
      const patterns = [
        '**/*.{js,ts,jsx,tsx,py,rs,go,java,c,cpp,h,hpp}',
        '**/*.{json,yaml,yml,toml,xml,md,txt}',
        '**/*.{css,scss,sass,less,html,vue,svelte}',
        '!node_modules/**',
        '!.git/**',
        '!dist/**',
        '!build/**',
        '!target/**',
        '!__pycache__/**',
        '!*.pyc',
        '!.env*',
      ];

      const files = await glob(patterns, {
        cwd: this.workingDirectory,
        dot: false,
        maxDepth: 5,
      });

      const fileNodes: FileNode[] = [];
      const maxFiles = 500; // Limit for performance

      for (const file of files.slice(0, maxFiles)) {
        try {
          const fullPath = path.join(this.workingDirectory, file);
          const stats = await fs.stat(fullPath);

          fileNodes.push({
            path: file,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.isFile() ? stats.size : undefined,
            modified: stats.mtime,
          });
        } catch (error) {
          // Skip files that can't be accessed
        }
      }

      this.context.projectStructure.files = fileNodes;
      logger.debug('Indexed project files', { count: fileNodes.length });
    } catch (error) {
      logger.error('Failed to index project files', error);
    }
  }

  private async discoverDependencies(): Promise<void> {
    try {
      const packageManager = this.context.projectStructure.packageManager;
      if (!packageManager) {
        return;
      }

      const packageInfo = await this.parsePackageInfo(packageManager);
      this.context.projectStructure.dependencies = packageInfo.dependencies;
      this.context.projectStructure.scripts = packageInfo.scripts;

      logger.debug('Discovered dependencies', {
        count: packageInfo.dependencies.length,
        scripts: Object.keys(packageInfo.scripts).length
      });
    } catch (error) {
      logger.error('Failed to discover dependencies', error);
    }
  }

  private async gatherEnvironmentInfo(): Promise<void> {
    try {
      const envInfo: EnvironmentInfo = {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        shell: process.env['SHELL'] || process.env['ComSpec'] || 'unknown',
        terminal: process.env['TERM'] || process.env['TERM_PROGRAM'] || 'unknown',
        workingDirectory: this.workingDirectory,
        homeDirectory: os.homedir(),
        pathVariable: (process.env['PATH'] || '').split(path.delimiter),
      };

      // Get npm version
      try {
        const npmVersion = await this.executeCommand('npm', ['--version']);
        envInfo.npmVersion = npmVersion.trim();
      } catch (error) {
        // npm might not be available
      }

      // Get git version
      try {
        const gitVersion = await this.executeCommand('git', ['--version']);
        envInfo.gitVersion = gitVersion.replace('git version ', '').trim();
      } catch (error) {
        // git might not be available
      }

      this.context.environmentInfo = envInfo;
    } catch (error) {
      logger.error('Failed to gather environment info', error);
    }
  }

  private async updateActiveProcesses(): Promise<void> {
    // This is a simplified implementation
    // In a real implementation, you might want to use platform-specific tools
    this.context.activeProcesses = [];
  }

  private async startFileWatcher(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
    }

    this.watcher = watch(this.workingDirectory, {
      ignored: /(^|[\/\\])\../, // ignore dotfiles
      persistent: true,
      ignoreInitial: true,
      depth: 2,
    });

    this.watcher.on('change', (filePath) => {
      this.context.recentFiles.unshift(filePath);
      this.context.recentFiles = this.context.recentFiles.slice(0, 10); // Keep last 10
      this.emit('file-changed', filePath);
    });

    this.watcher.on('add', (filePath) => {
      this.context.recentFiles.unshift(filePath);
      this.context.recentFiles = this.context.recentFiles.slice(0, 10);
      this.emit('file-added', filePath);
    });

    this.watcher.on('unlink', (filePath) => {
      this.emit('file-deleted', filePath);
    });
  }

  private async readJsonFile(filename: string): Promise<any> {
    try {
      const content = await fs.readFile(path.join(this.workingDirectory, filename), 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      return null;
    }
  }

  private async readTomlFile(filename: string): Promise<any> {
    try {
      const content = await fs.readFile(path.join(this.workingDirectory, filename), 'utf-8');
      // Simple TOML parsing - in production, use a proper TOML parser
      const lines = content.split('\n');
      const result: any = {};
      let currentSection = result;
      
      for (const line of lines) {
        const trimmed = line.trim();
        if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
          const section = trimmed.slice(1, -1);
          currentSection = result[section] = {};
        } else if (trimmed.includes('=')) {
          const [key, value] = trimmed.split('=', 2);
          currentSection[key.trim()] = value.trim().replace(/"/g, '');
        }
      }
      
      return result;
    } catch (error) {
      return null;
    }
  }

  private async executeCommand(command: string, args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
      const child = spawn(command, args, { stdio: 'pipe' });
      let output = '';
      
      child.stdout?.on('data', (data) => {
        output += data.toString();
      });
      
      child.on('close', (code) => {
        if (code === 0) {
          resolve(output);
        } else {
          reject(new Error(`Command failed with code ${code}`));
        }
      });
      
      child.on('error', reject);
    });
  }

  async shutdown(): Promise<void> {
    if (this.watcher) {
      await this.watcher.close();
      this.watcher = null;
    }
    
    this.removeAllListeners();
    logger.info('Context manager shutdown');
  }
}
