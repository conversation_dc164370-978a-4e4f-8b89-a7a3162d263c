import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import path from 'path';
import os from 'os';
import fs from 'fs/promises';
import { logger } from '@/utils/logger';
import { TokenCounter } from '@/utils/tokenCounter';
import type {
  Session,
  SessionContext,
  SessionMetadata,
  ConversationMessage,
  ToolResult,
} from '@/types';

export class SessionManager extends EventEmitter {
  private sessionsDir: string;
  private sessionCache = new Map<string, { session: Session; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  constructor() {
    super();
    this.sessionsDir = path.join(os.homedir(), '.kritrima', 'sessions');
  }

  async initialize(): Promise<void> {
    try {
      // Ensure directory exists
      await fs.mkdir(this.sessionsDir, { recursive: true });

      logger.info('Session manager initialized', { sessionsDir: this.sessionsDir });
    } catch (error) {
      logger.error('Failed to initialize session manager', error);
      throw error;
    }
  }

  private getSessionPath(sessionId: string): string {
    return path.join(this.sessionsDir, `${sessionId}.json`);
  }

  private async readSessionFile(sessionId: string): Promise<Session | null> {
    try {
      const sessionPath = this.getSessionPath(sessionId);
      const content = await fs.readFile(sessionPath, 'utf-8');
      const data = JSON.parse(content);

      // Convert date strings back to Date objects
      data.createdAt = new Date(data.createdAt);
      data.updatedAt = new Date(data.updatedAt);
      data.messages = data.messages.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp),
      }));

      return data;
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return null; // File doesn't exist
      }
      throw error;
    }
  }

  private async writeSessionFile(session: Session): Promise<void> {
    const sessionPath = this.getSessionPath(session.id);
    const content = JSON.stringify(session, null, 2);
    await fs.writeFile(sessionPath, content, 'utf-8');
  }

  async createSession(options: {
    workingDirectory: string;
    context: SessionContext;
  }): Promise<Session> {
    const sessionId = nanoid();
    const now = new Date();

    const metadata: SessionMetadata = {
      totalCommands: 0,
      successfulCommands: 0,
      failedCommands: 0,
      lastActivity: now,
      tags: [],
    };

    const session: Session = {
      id: sessionId,
      createdAt: now,
      updatedAt: now,
      workingDirectory: options.workingDirectory,
      context: options.context,
      messages: [],
      metadata,
    };

    try {
      await this.writeSessionFile(session);

      logger.info('Session created', { sessionId, workingDirectory: options.workingDirectory });
      this.emit('session-created', session);

      return session;
    } catch (error) {
      logger.error('Failed to create session', error);
      throw error;
    }
  }

  async loadSession(sessionId: string, useCache = true): Promise<Session> {
    try {
      // Check cache first
      if (useCache) {
        const cached = this.sessionCache.get(sessionId);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
          logger.debug('Session loaded from cache', { sessionId });
          return { ...cached.session }; // Return a copy to prevent mutations
        }
      }

      const session = await this.readSessionFile(sessionId);

      if (!session) {
        throw new Error(`Session not found: ${sessionId}`);
      }

      // Cache the session
      if (useCache) {
        this.sessionCache.set(sessionId, {
          session: { ...session },
          timestamp: Date.now()
        });
      }

      logger.debug('Session loaded from disk', { sessionId });
      return session;
    } catch (error) {
      logger.error('Failed to load session', error);
      throw error;
    }
  }

  async saveSession(session: Session): Promise<void> {
    try {
      session.updatedAt = new Date();
      await this.writeSessionFile(session);

      // Update cache
      this.sessionCache.set(session.id, {
        session: { ...session },
        timestamp: Date.now()
      });

      logger.debug('Session saved', { sessionId: session.id });
    } catch (error) {
      logger.error('Failed to save session', error);
      throw error;
    }
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessionPath = this.getSessionPath(sessionId);
      await fs.unlink(sessionPath);

      // Remove from cache
      this.sessionCache.delete(sessionId);

      logger.info('Session deleted', { sessionId });
      this.emit('session-deleted', { sessionId });
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        throw new Error(`Session not found: ${sessionId}`);
      }
      logger.error('Failed to delete session', error);
      throw error;
    }
  }

  private clearExpiredCache(): void {
    const now = Date.now();
    for (const [sessionId, cached] of this.sessionCache.entries()) {
      if (now - cached.timestamp > this.cacheTimeout) {
        this.sessionCache.delete(sessionId);
      }
    }
  }

  async getSessions(limit = 50): Promise<Session[]> {
    try {
      // Clear expired cache entries
      this.clearExpiredCache();

      const files = await fs.readdir(this.sessionsDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));

      const sessions: Session[] = [];

      for (const file of sessionFiles.slice(0, limit)) {
        const sessionId = path.basename(file, '.json');
        try {
          const session = await this.readSessionFile(sessionId);
          if (session) {
            // Don't load full context and messages for list view
            sessions.push({
              ...session,
              context: {} as SessionContext,
              messages: [],
            });
          }
        } catch (error) {
          // Skip corrupted session files
          logger.warn('Skipping corrupted session file', { file, error });
        }
      }

      // Sort by updated date
      sessions.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());

      return sessions;
    } catch (error) {
      logger.error('Failed to get sessions', error);
      throw error;
    }
  }

  async addMessage(sessionId: string, message: ConversationMessage): Promise<void> {
    try {
      const session = await this.loadSession(sessionId);
      session.messages.push(message);
      session.updatedAt = new Date();
      await this.saveSession(session);

      logger.debug('Message added', { sessionId, messageId: message.id, role: message.role });
      this.emit('message-added', { sessionId, message });
    } catch (error) {
      logger.error('Failed to add message', error);
      throw error;
    }
  }

  async getMessages(sessionId: string, limit = 20): Promise<ConversationMessage[]> {
    try {
      const session = await this.loadSession(sessionId);

      // Use a more conservative default limit to prevent token overflow
      // The AI provider will further truncate based on actual token counts
      const messages = session.messages.slice(-limit);

      logger.debug('Retrieved messages', {
        sessionId,
        totalMessages: session.messages.length,
        returnedMessages: messages.length,
        limit
      });

      return messages;
    } catch (error) {
      logger.error('Failed to get messages', error);
      throw error;
    }
  }

  /**
   * Get messages with token-aware limiting
   */
  async getMessagesWithTokenLimit(
    sessionId: string,
    model: string,
    maxTokens?: number,
    messageLimit?: number
  ): Promise<ConversationMessage[]> {
    try {
      const session = await this.loadSession(sessionId);

      if (session.messages.length === 0) {
        return [];
      }

      // Calculate optimal message limit based on model
      const modelLimit = TokenCounter.getModelTokenLimit(model);
      const targetLimit = maxTokens || Math.floor(modelLimit * 0.6); // Use 60% of model limit for messages

      // Apply message count limit first if specified
      const recentMessages = messageLimit
        ? session.messages.slice(-messageLimit)
        : session.messages;

      // Start with recent messages and work backwards
      const messages: ConversationMessage[] = [];
      let currentTokens = 0;

      for (let i = recentMessages.length - 1; i >= 0; i--) {
        const message = recentMessages[i];
        const messageTokens = TokenCounter.countMessageTokens(message);

        if (currentTokens + messageTokens <= targetLimit) {
          messages.unshift(message);
          currentTokens += messageTokens;
        } else {
          break;
        }
      }

      logger.debug('Retrieved messages with token limit', {
        sessionId,
        model,
        totalMessages: session.messages.length,
        returnedMessages: messages.length,
        estimatedTokens: currentTokens,
        targetLimit
      });

      return messages;
    } catch (error) {
      logger.error('Failed to get messages with token limit', error);
      throw error;
    }
  }

  async updateToolResult(sessionId: string, result: ToolResult): Promise<void> {
    try {
      const session = await this.loadSession(sessionId);

      // Update session metadata
      session.metadata.totalCommands++;
      if (result.success) {
        session.metadata.successfulCommands++;
      } else {
        session.metadata.failedCommands++;
      }
      session.metadata.lastActivity = new Date();

      await this.saveSession(session);

      logger.debug('Tool result updated', { sessionId, resultId: result.id });
    } catch (error) {
      logger.error('Failed to update tool result', error);
      throw error;
    }
  }

  async searchSessions(query: string): Promise<Session[]> {
    try {
      const sessions = await this.getSessions();
      const lowerQuery = query.toLowerCase();

      return sessions.filter(session => {
        return session.workingDirectory.toLowerCase().includes(lowerQuery) ||
               session.messages.some(msg => msg.content.toLowerCase().includes(lowerQuery));
      }).slice(0, 20);
    } catch (error) {
      logger.error('Failed to search sessions', error);
      throw error;
    }
  }

  async cleanup(olderThanDays = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const files = await fs.readdir(this.sessionsDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));

      let deletedCount = 0;

      for (const file of sessionFiles) {
        const sessionId = path.basename(file, '.json');
        try {
          const session = await this.readSessionFile(sessionId);
          if (session && session.updatedAt < cutoffDate) {
            await this.deleteSession(sessionId);
            deletedCount++;
          }
        } catch (error) {
          // Skip corrupted files
        }
      }

      logger.info('Session cleanup completed', {
        deletedSessions: deletedCount,
        olderThanDays
      });

      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup sessions', error);
      throw error;
    }
  }

  async close(): Promise<void> {
    this.sessionCache.clear();
    logger.info('Session manager closed');
  }
}
