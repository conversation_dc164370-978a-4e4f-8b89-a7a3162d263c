{"version": 3, "file": "tokenCounter.js", "sourceRoot": "", "sources": ["../../src/utils/tokenCounter.ts"], "names": [], "mappings": ";;;AAEA;;;GAGG;AACH,MAAa,YAAY;IACf,MAAM,CAAU,eAAe,GAAG,CAAC,CAAC;IACpC,MAAM,CAAU,aAAa,GAAG,GAAG,CAAC,CAAC,oBAAoB;IAEjE;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,IAAI,CAAC,IAAI;YAAE,OAAO,CAAC,CAAC;QACpB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,OAA4B;QACpD,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,uBAAuB;QACvB,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAElD,0BAA0B;QAC1B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACzC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;QAED,4DAA4D;QAC5D,MAAM,IAAI,EAAE,CAAC;QAEb,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,QAA+B;QACxD,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CACrB,QAA+B,EAC/B,SAAiB,EACjB,sBAA8B,CAAC,EAC/B,cAAsB,CAAC;QAEvB,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,mBAAmB,GAAG,WAAW,CAAC;QAE7G,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,sEAAsE;QACtE,IAAI,oBAAoB,GAAG,CAAC,CAAC,CAAC;QAC9B,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAChC,oBAAoB,GAAG,CAAC,CAAC;gBACzB,MAAM;YACR,CAAC;QACH,CAAC;QAED,MAAM,MAAM,GAA0B,EAAE,CAAC;QACzC,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,yDAAyD;QACzD,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAEvD,uCAAuC;YACvC,IAAI,CAAC,KAAK,oBAAoB,EAAE,CAAC;gBAC/B,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,aAAa,IAAI,aAAa,CAAC;gBAC/B,SAAS;YACX,CAAC;YAED,mCAAmC;YACnC,IAAI,aAAa,GAAG,aAAa,IAAI,eAAe,EAAE,CAAC;gBACrD,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxB,aAAa,IAAI,aAAa,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,MAAM;YACR,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,IAAY,EAAE,SAAiB;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,eAAe,IAAI,SAAS,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;QACzF,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,KAAa;QACrC,MAAM,MAAM,GAA2B;YACrC,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,MAAM;YACrB,QAAQ,EAAE,MAAM;YAChB,eAAe,EAAE,IAAI;YACrB,mBAAmB,EAAE,KAAK;YAC1B,eAAe,EAAE,KAAK;YACtB,gBAAgB,EAAE,KAAK;YACvB,iBAAiB,EAAE,MAAM;YACzB,eAAe,EAAE,MAAM;YACvB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,KAAK;SACnB,CAAC;QAEF,qDAAqD;QACrD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CACjC,KAAa,EACb,mBAA2B,EAC3B,WAAmB,EACnB,sBAA8B,IAAI;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAClD,MAAM,oBAAoB,GAAG,UAAU,GAAG,mBAAmB,GAAG,WAAW,GAAG,mBAAmB,CAAC;QAElG,uDAAuD;QACvD,MAAM,mBAAmB,GAAG,GAAG,CAAC;QAEhC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,oBAAoB,GAAG,mBAAmB,CAAC,CAAC,CAAC;IAC7E,CAAC;;AApJH,oCAqJC"}